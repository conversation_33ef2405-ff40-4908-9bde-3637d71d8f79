# Claude Code Setup for Y3DHub 🎉

Your Y3DHub repository is now configured with the official Claude Code GitHub Action.

## Setup Complete! ✅

The workflow uses the official `anthropics/claude-code-action@beta` with:
- **Custom Y3DHub Context**: Pre-configured for 3D printing workflows
- **Development Tools**: npm, Prisma, build commands enabled  
- **Smart Responses**: Tailored to Next.js, TypeScript, and your tech stack

## Quick Start

1. **Verify Setup**: Ensure `ANTHROPIC_API_KEY` is in GitHub repository secrets
2. **Install Claude App**: Visit https://github.com/apps/claude (if not done)
3. **Test**: Comment `@claude hello` on any PR or issue

## Usage Examples

### Ask Questions
```
@claude What does this function do?
@claude How can I optimize this database query?
```

### Request Changes  
```
@claude Can you add error handling to this API route?
@claude Please review this PR for security issues
```

### Get Analysis
```
@claude Analyze this 3D printing workflow optimization
@claude Review the ShipStation integration patterns
```

## Configuration Files

- **`.claude/config.yaml`** - Customizable behavior settings
- **`.claude/styleguide.md`** - Y3DHub-specific code guidelines
- **`.claude/commands.md`** - Command reference and examples

## Y3DHub Context Included

Claude automatically knows about:
- **Tech Stack**: Next.js 14+, TypeScript, Prisma, Tailwind CSS
- **APIs**: ShipStation, OpenAI, Amazon SP-API
- **3D Printing**: STL processing, print queues, OpenSCAD
- **Best Practices**: Performance, security, type safety

## Troubleshooting

- **No Response?** Check API key in repository secrets
- **Permissions?** Ensure you have write access to the repository  
- **Still Issues?** Check workflow logs in Actions tab

For more details: https://github.com/anthropics/claude-code-action
