# Claude Code Commands Reference

## Available Commands

### Slash Commands
Use these commands in GitHub comments to trigger specific Claude behaviors:

- `/claude summary` - Generate a summary of pull request changes
- `/claude review` - Perform detailed code review following project guidelines
- `/claude help` - Show available commands and features
- `@claude [your question]` - Ask any question about the code

### Automatic Triggers

Claude will automatically respond to:
- New pull requests that mention `@claude` in title or description
- New issues that mention `@claude` in title or description
- Comments on pull requests or issues that mention `@claude` or use `/claude` commands
- Pull request reviews that mention `@claude`

## Configuration

<PERSON>'s behavior is customized through files in the `.claude/` directory:

### `.claude/config.yaml`
Controls general behavior including:
- Files to ignore during analysis
- Code review focus areas
- Response formatting preferences
- Project-specific technology settings

### `.claude/styleguide.md`
Defines project-specific guidelines for:
- Next.js App Router best practices
- Database and Prisma patterns
- API integration standards (ShipStation, OpenAI)
- Security guidelines
- 3D printing code standards
- TypeScript and React patterns

## Example Usage

### Get PR Summary
```
/claude summary
```
Provides a concise overview of what changed in the pull request.

### Request Code Review
```
/claude review
```
Performs detailed analysis focusing on:
- Security vulnerabilities
- Performance issues
- Best practice adherence
- Y3DHub-specific patterns

### Ask Specific Questions
```
@claude How can I optimize this Prisma query for better performance?
```

```
@claude Is this ShipStation API integration following our security guidelines?
```

```
@claude What's the best way to handle errors in this Next.js API route?
```

### Get Help
```
/claude help
```
Shows available commands and project context.

## What Claude Focuses On

Based on the Y3DHub project configuration, Claude pays special attention to:

### Technology Stack
- Next.js 14 App Router patterns
- React 18 best practices
- TypeScript strict typing
- Prisma ORM optimization
- MySQL query efficiency
- TailwindCSS usage
- OpenSCAD 3D modeling

### Integrations
- ShipStation API security and error handling
- OpenAI API usage and token management
- NextAuth.js authentication flows

### 3D Printing Specific
- OpenSCAD code quality
- STL file handling
- 3D modeling best practices
- Rendering optimization

### Code Quality
- Security vulnerabilities
- Performance bottlenecks
- Accessibility compliance
- Mobile responsiveness
- Test coverage
- Documentation completeness

## Response Format

Claude responses include:
- 📝 Clear markdown formatting
- 🔍 Code examples when helpful
- ⚠️ Security and performance warnings
- ✅ Specific improvement suggestions
- 📚 References to project guidelines

## Tips for Best Results

1. **Be specific** - Ask detailed questions for better answers
2. **Provide context** - Mention what you're trying to achieve
3. **Use commands** - Leverage `/claude` commands for focused responses
4. **Reference files** - Claude can analyze specific files when mentioned
5. **Follow up** - Ask clarifying questions if needed

## Privacy and Security

- Claude only analyzes code in public repositories or those you have access to
- API keys and sensitive data in `.env` files are ignored
- Claude follows the ignore patterns defined in `.claude/config.yaml`
- All interactions are logged in GitHub for transparency
