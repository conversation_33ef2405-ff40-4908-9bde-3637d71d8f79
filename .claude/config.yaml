# Claude Code Assist Configuration
# Configuration for Claude Code behavior in this repository

# Schema for validation
$schema: "http://json-schema.org/draft-07/schema#"
title: ClaudeConfig
description: Configuration for Claude Code Assist on Y3DHub repository
type: object

# General settings
have_fun: true  # Enable creative and engaging responses
verbose: false  # Detailed explanations vs concise responses

# Files and directories to ignore during analysis
ignore_patterns:
  - "node_modules/**"
  - "dist/**"
  - "build/**"
  - ".next/**"
  - "coverage/**"
  - "*.log"
  - "*.tmp"
  - "fonts/**"
  - "output_stl/**"
  - "logs/**"
  - ".env*"

# Code review configuration
code_review:
  # Enable/disable automatic code reviews
  enabled: true
  
  # Comment severity threshold (LOW, MEDIUM, HIGH, CRITICAL)
  comment_severity_threshold: "MEDIUM"
  
  # Maximum number of review comments (-1 for unlimited)
  max_review_comments: 10
  
  # Focus areas for code review
  focus_areas:
    - "correctness"      # Logic errors, edge cases, API usage
    - "security"         # Vulnerabilities, data handling
    - "performance"      # Bottlenecks, optimization opportunities
    - "maintainability"  # Readability, best practices
    - "testing"          # Test coverage, test quality
  
  # Pull request opened behavior
  pull_request_opened:
    help_message: false        # Post help message on PR open
    summary: true             # Generate PR summary
    automatic_review: true    # Perform automatic code review
    
  # Issue analysis settings
  issue_analysis:
    enabled: true
    provide_solutions: true
    suggest_implementation: true

# Project-specific settings for Y3DHub
project_specific:
  # Technology stack focus
  technologies:
    - "nextjs"
    - "react"
    - "typescript"
    - "prisma"
    - "mysql"
    - "tailwindcss"
    - "openscad"
    - "shipstation-api"
    - "openai-api"
  
  # Special file types to pay attention to
  special_files:
    database_schema: "prisma/schema.prisma"
    api_routes: "src/app/api/**/*.ts"
    components: "src/components/**/*.tsx"
    openscad_files: "**/*.scad"
    config_files: ["next.config.cjs", "tailwind.config.ts", "tsconfig.json"]
  
  # Y3DHub specific checks
  custom_checks:
    shipstation_api: true     # Review ShipStation API integration
    openai_usage: true        # Check OpenAI API usage patterns
    prisma_migrations: true   # Validate database migrations
    openscad_best_practices: true  # 3D modeling code review
    nextjs_app_router: true   # Next.js App Router best practices
    security_env_vars: true   # Environment variable security

# Command behavior
commands:
  # Default behavior for different commands
  summary:
    include_file_changes: true
    include_commit_messages: true
    highlight_breaking_changes: true
  
  review:
    include_suggestions: true
    provide_examples: true
    reference_documentation: true
  
  help:
    show_project_context: true
    include_available_commands: true

# Response formatting
response_format:
  use_markdown: true
  include_code_blocks: true
  add_emoji: true
  structure_with_headers: true
