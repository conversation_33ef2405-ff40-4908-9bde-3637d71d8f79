# Y3DHub Style Guide for Claude Code Assist

This document provides specific guidelines and best practices that <PERSON> should follow when reviewing code for the Y3DHub project.

## Project Overview
Y3DHub is a 3D printing management platform built with Next.js, integrating with ShipStation API and OpenAI for intelligent text extraction and task management.

## Code Review Priorities

### 1. Next.js App Router Best Practices
- **File-based routing**: Ensure proper use of App Router conventions
- **Server Components**: Prefer Server Components by default, use Client Components only when necessary
- **Loading states**: Implement proper loading.tsx and error.tsx files
- **Metadata**: Include appropriate metadata for SEO
- **Performance**: Check for proper use of dynamic imports and code splitting

### 2. Database & Prisma Guidelines
- **Schema changes**: Always review migrations for breaking changes
- **Query optimization**: Flag N+1 queries and suggest batching
- **Type safety**: Ensure Prisma types are properly used
- **Transactions**: Check for proper transaction usage in complex operations
- **Indexes**: Suggest database indexes for frequently queried fields

### 3. API Integration Standards

#### ShipStation API
- **Error handling**: Robust error handling for API failures
- **Rate limiting**: Respect API rate limits and implement backoff strategies
- **Data validation**: Validate all incoming data from ShipStation
- **Webhook security**: Verify webhook signatures when applicable

#### OpenAI API
- **Prompt engineering**: Review prompts for clarity and effectiveness
- **Token management**: Monitor token usage and implement cost controls
- **Error handling**: Handle API errors gracefully with fallbacks
- **Content filtering**: Ensure appropriate content filtering

### 4. Security Guidelines
- **Environment variables**: Never commit sensitive data
- **API keys**: Proper rotation and secure storage practices
- **Input validation**: Sanitize all user inputs
- **CORS**: Proper CORS configuration for API routes
- **Authentication**: Secure authentication flows with NextAuth.js

### 5. 3D Printing Specific Code

#### OpenSCAD Integration
- **Parameterization**: Use variables instead of magic numbers
- **Error handling**: Graceful handling of rendering failures
- **File organization**: Modular design with clear function separation
- **Documentation**: Comment complex mathematical operations
- **Performance**: Optimize for rendering speed when possible

#### STL File Management
- **File validation**: Verify STL file integrity
- **Storage optimization**: Efficient file storage and retrieval
- **Metadata tracking**: Maintain proper file metadata
- **Cleanup**: Remove temporary files after processing

### 6. TypeScript Standards
- **Strict typing**: Use strict TypeScript configurations
- **Interface definitions**: Prefer interfaces over types for object shapes
- **Generic types**: Use generics for reusable components
- **Enum usage**: Use const assertions instead of enums when appropriate
- **Error types**: Define custom error types for better error handling

### 7. React Component Guidelines
- **Component composition**: Favor composition over inheritance
- **Props interface**: Clear and documented prop interfaces
- **State management**: Use appropriate state management patterns
- **Performance**: Implement React.memo, useMemo, useCallback where beneficial
- **Accessibility**: Include proper ARIA attributes and semantic HTML

### 8. Testing Requirements
- **Unit tests**: Cover critical business logic
- **Integration tests**: Test API routes and database operations
- **E2E tests**: Cover critical user journeys
- **Error scenarios**: Test error conditions and edge cases
- **Mock strategies**: Proper mocking of external APIs

### 9. Performance Considerations
- **Bundle size**: Monitor and optimize bundle sizes
- **Image optimization**: Use Next.js Image component properly
- **Caching**: Implement appropriate caching strategies
- **Database queries**: Optimize database query patterns
- **Memory leaks**: Check for potential memory leaks in long-running processes

### 10. Code Organization
- **File structure**: Follow Next.js App Router conventions
- **Import organization**: Group imports logically (external, internal, relative)
- **Naming conventions**: Use descriptive, consistent naming
- **Code splitting**: Organize code into logical modules
- **Documentation**: Include JSDoc comments for complex functions

## Review Checklist

When reviewing code, Claude should check for:

- [ ] Proper error handling and user feedback
- [ ] Security vulnerabilities (SQL injection, XSS, etc.)
- [ ] Performance implications
- [ ] Accessibility compliance
- [ ] Mobile responsiveness
- [ ] TypeScript type safety
- [ ] Test coverage for new features
- [ ] Documentation updates
- [ ] Environment variable usage
- [ ] API integration best practices
- [ ] Database query optimization
- [ ] 3D printing workflow efficiency

## Common Issues to Flag

### High Priority
- Hardcoded API keys or secrets
- SQL injection vulnerabilities
- Missing error boundaries
- Unhandled promise rejections
- Memory leaks in components

### Medium Priority
- Missing TypeScript types
- Inefficient database queries
- Poor component performance
- Accessibility issues
- Missing tests for critical paths

### Low Priority
- Code style inconsistencies
- Missing documentation
- Unused imports or variables
- Opportunities for code reuse

## Project-Specific Patterns

### API Route Structure
```typescript
// Preferred pattern for API routes
export async function POST(request: Request) {
  try {
    const body = await request.json();
    // Validate input
    // Process request
    // Return structured response
  } catch (error) {
    return NextResponse.json(
      { error: 'Error message' }, 
      { status: 500 }
    );
  }
}
```

### Database Operation Pattern
```typescript
// Preferred pattern for database operations
export async function createOrder(data: OrderData) {
  return await prisma.$transaction(async (tx) => {
    // Perform related operations in transaction
  });
}
```

### Component Error Handling
```typescript
// Preferred error boundary pattern
function Component() {
  const [error, setError] = useState<string | null>(null);
  
  if (error) {
    return <ErrorDisplay message={error} />;
  }
  
  // Component logic
}
```

This style guide should be referenced when providing code reviews and suggestions for the Y3DHub project.
