name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  audit:
    name: Audit & Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'npm'
      - name: Install dependencies
        run: npm ci

      - name: Cache ESLint cache
        uses: actions/cache@v3
        with:
          path: .eslintcache
          key: ${{ runner.os }}-eslintcache-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-eslintcache-
      - name: Lint (cached)
        run: npm run lint:ci
        continue-on-error: true

      - name: Run tests
        run: npm test
