name: <PERSON> Code

on:
  issue_comment:
    types: [created]
  pull_request_review_comment:
    types: [created]
  issues:
    types: [opened, assigned]
  pull_request_review:
    types: [submitted]
  pull_request:
    types: [opened, synchronize]  # For automatic code review
  workflow_dispatch:  # For manual triggering
    inputs:
      prompt:
        description: 'Custom prompt for <PERSON>'
        required: true
        type: string

jobs:
  claude-response:
    runs-on: ubuntu-latest
    permissions:
      contents: write        # Allow Claude to create commits and files
      pull-requests: write   # Allow <PERSON> to create and update PRs
      issues: write         # Allow Claude to create and update issues
      id-token: write       # For GitHub's OIDC token
      actions: read         # Allow reading workflow information
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Full history for better context
          
      - uses: anthropics/claude-code-action@beta
        with:
          anthropic_api_key: ${{ secrets.ANTHROPIC_API_KEY }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          # Custom trigger phrases for Y3DHub
          trigger_phrase: "@claude"
          # Additional tools for development workflow
          allowed_tools: "<PERSON><PERSON>(npm install),<PERSON><PERSON>(npm run test),<PERSON><PERSON>(npm run build),<PERSON><PERSON>(npx prisma generate),<PERSON><PERSON>(npx prisma db push),Edit,Replace"
          # Custom instructions for Y3DHub project context
          custom_instructions: |
            You are helping with the Y3DHub 3D printing management platform. This is a Next.js application with:
            
            - **Tech Stack**: Next.js 14+ (App Router), TypeScript, Prisma ORM, Tailwind CSS
            - **Integrations**: ShipStation API, OpenAI API, Amazon SP-API, OpenSCAD
            - **Key Areas**: 3D printing workflows, order management, shipping automation, AI-powered design tools
            - **Database**: PostgreSQL with Prisma ORM
            - **Deployment**: Vercel with Docker support
            
            Please follow these project-specific guidelines:
            
            1. **Code Style**: Follow the guidelines in `.claude/styleguide.md` if it exists
            2. **API Routes**: Use Next.js App Router patterns (`app/api/` directory)
            3. **Database**: Use Prisma for all database operations
            4. **Types**: Maintain strict TypeScript typing
            5. **UI Components**: Use Tailwind CSS and shadcn/ui components
            6. **3D Printing Context**: Consider STL file handling, print queue management, and material specifications
            7. **Integration Patterns**: Follow established patterns for ShipStation, OpenAI, and Amazon APIs
            
            When reviewing code or implementing features, prioritize:
            - Performance optimization for 3D file processing
            - Error handling for external API integrations
            - Type safety and data validation
            - User experience for 3D printing workflows
            - Security for API keys and customer data
          # Specify model (optional - defaults to latest)
          model: "claude-3-5-sonnet-20241022"
          # Timeout for longer operations
          timeout_minutes: 45
