# Product Name Sync Changes

## Problem Solved

The crontab workflow script was overwriting manual product name changes made via the "Update All" button in the View Task dialog. This happened because:

1. **Frequent Crontab Execution**: The workflow runs every 1-15 minutes
2. **Automatic Name Updates**: ShipStation sync was updating product names from ShipStation data
3. **Manual Changes Lost**: Your manual corrections were being overwritten

## Solution Implemented

### 1. **Product Name Updates Now Disabled by Default**

- **New Products**: Still get names from ShipStation when first created
- **Existing Products**: Names are preserved and NOT updated from ShipStation
- **Manual Changes**: Your "Update All" changes will no longer be overwritten

### 2. **Optional Flag to Enable Name Updates**

Added `--update-product-names` flag to the sync-orders script:

```bash
# Default behavior (preserves existing product names)
npm run sync-orders

# Enable product name updates from ShipStation
npm run sync-orders -- --update-product-names
```

### 3. **Workflow Script Updated**

The `scripts/workflow.sh` script now defaults to preserving product names unless explicitly told otherwise.

## Technical Changes Made

### Files Modified:

1. **`src/lib/shipstation/mappers.ts`**
   - Added `updateNames` option to `mapSsItemToProductData()`
   - Only includes product name in update data when explicitly requested

2. **`src/lib/shipstation/db-sync.ts`**
   - Modified `upsertProductFromItem()` to distinguish between create vs update operations
   - New products always get names, existing products preserve names unless flag is set
   - Added logging when name updates are disabled

3. **`src/scripts/sync-orders.ts`**
   - Added `--update-product-names` command-line flag
   - Passes flag through to sync functions

4. **`src/lib/shipstation/types.ts`**
   - Extended `SyncOptions` and `ShipStationSyncProcessParams` to include `updateProductNames` option

5. **`src/lib/shipstation/api.ts`**
   - Updated `syncAllPaginatedOrders()` to pass `updateProductNames` option to `upsertOrderWithItems()`

## How to Use

### Normal Operation (Recommended)
```bash
# Your crontab workflow will now preserve manual product name changes
cd /home/<USER>/Y3DHub_prod && ./scripts/workflow.sh
```

### When You Want to Update Names from ShipStation
```bash
# One-time sync with name updates enabled
npm run sync-orders -- --update-product-names

# Or with specific parameters
npm run sync-orders -- --mode recent --hours 24 --update-product-names
```

### Workflow Script Usage
```bash
# Default: preserves product names
./scripts/workflow.sh

# To enable name updates, you would need to modify the workflow script
# or run sync-orders separately with the flag
```

## Benefits

✅ **Manual Changes Preserved**: Your "Update All" button changes won't be overwritten  
✅ **New Products Still Added**: ShipStation will still create new products with names  
✅ **Backward Compatible**: Existing functionality unchanged, just safer defaults  
✅ **Flexible Control**: Can still enable name updates when needed  
✅ **Clear Logging**: System logs when name updates are disabled  

## Memory Added

The system now remembers: "Product names in the system should be treated as the source of truth, and ShipStation name changes should not be automatically tracked/synced."

## Verification

To verify the changes are working:

1. **Make a manual product name change** via "Update All" button
2. **Wait for the next crontab run** (1-15 minutes)
3. **Check that your change is preserved** - it should not be overwritten

If you need to enable name updates for a specific sync, use the `--update-product-names` flag.
