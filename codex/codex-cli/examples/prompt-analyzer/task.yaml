name: "prompt-analyzer"
description: |
  I have some existing work here (embedding prompts, clustering them, generating
  summaries with GPT). I want to make it more interactive and reusable.

  Objective: create an interactive cluster explorer
     - Build a lightweight streamlit app UI
     - Allow users to upload a CSV of prompts
     - Display clustered prompts with auto-generated cluster names and summaries
     - Click "cluster" and see progress stream in a small window (primarily for aesthetic reasons)
     - Let users browse examples by cluster, view outliers, and inspect individual prompts
     - See generated analysis rendered in the app, along with the plots displayed nicely
     - Support selecting clustering algorithms (e.g. DBSCAN, KMeans, etc) and "recluster"
     - Include token count + histogram of prompt lengths
     - Add interactive filters in UI (e.g. filter by token length, keyword, or cluster)

  When you're done, update the README.md with a changelog and instructions for how to run the app.
