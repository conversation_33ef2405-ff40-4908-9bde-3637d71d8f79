# ShipStation Integration

## Overview

Y3DHub integrates with ShipStation to synchronize order data and manage the print queue workflow. This integration handles order synchronization, product management, and status updates.

## Product Name Management

### Default Behavior (Recommended)

**Product names are treated as the source of truth in Y3DHub**, not ShipStation. This prevents manual product name corrections from being overwritten.

- ✅ **New Products**: Get initial names from ShipStation when first created
- ✅ **Existing Products**: Names are preserved and NOT updated from ShipStation
- ✅ **Manual Changes**: "Update All" button changes are protected from being overwritten
- ✅ **Crontab Safe**: Automated workflows won't overwrite your manual corrections

### Enabling Product Name Updates

When you specifically want to update product names from ShipStation:

```bash
# Enable product name updates for a one-time sync
npm run sync-orders -- --update-product-names

# With specific parameters
npm run sync-orders -- --mode recent --hours 24 --update-product-names
```

### Why This Approach?

1. **ShipStation names are often messy** - They come from various marketplaces with inconsistent formatting
2. **Manual control is needed** - You need clean, standardized names for your print queue
3. **Prevents data loss** - Your manual corrections won't be accidentally overwritten
4. **Workflow safety** - Crontab automation won't interfere with manual changes

## Configuration

Set the following environment variables:

```bash
SHIPSTATION_API_KEY=your_api_key
SHIPSTATION_API_SECRET=your_api_secret
```

## Key Features

- **Order Synchronization**: Automatic syncing of orders, customers, and order items
- **Product Management**: Smart product name handling with manual override protection
- **Status Updates**: Automatic task completion when orders ship
- **Error Handling**: Comprehensive logging and error recovery
- **Dry Run Support**: Test synchronization without making changes
