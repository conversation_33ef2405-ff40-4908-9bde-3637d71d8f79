# Operations Documentation

This directory contains operational guides and procedures for Y3DHub.

## Available Guides

- **[Product Name Management](product-name-management.md)** - How to manage product names, ShipStation sync behavior, and manual overrides
- **[Crontab Updates](crontab-updated.md)** - Crontab configuration and scheduling information

## Quick Reference

### Product Names
- Y3DHub product names are the source of truth
- Manual changes via "Update All" are preserved
- Use `--update-product-names` flag only when needed

### Common Operations
```bash
# Standard sync (preserves product names)
npm run sync-orders

# Sync with name updates (use sparingly)
npm run sync-orders -- --update-product-names

# Full workflow
./scripts/workflow.sh
```
