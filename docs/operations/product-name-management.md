---
title: Product Name Management
last-reviewed: 2025-01-24
maintainer: Y3DHub Team
---

# Product Name Management

This document explains how Y3DHub handles product names, the relationship with ShipStation, and how to manage product names effectively.

## Overview

**Y3DHub treats product names as the source of truth**, not ShipStation. This design prevents manual product name corrections from being accidentally overwritten by automated workflows.

## Default Behavior

### ✅ What Happens Automatically

- **New Products**: When a new product is encountered from ShipStation, it gets the initial name from ShipStation
- **Existing Products**: Names are preserved and NOT updated from ShipStation during sync
- **Manual Changes**: Changes made via the "Update All" button are protected from being overwritten
- **Crontab Safety**: Automated workflows (running every 1-15 minutes) won't interfere with manual corrections

### ❌ What Doesn't Happen

- Existing product names are NOT automatically updated from ShipStation
- Manual corrections are NOT overwritten by sync operations
- ShipStation name changes do NOT automatically propagate to Y3DHub

## Manual Product Name Management

### Using the "Update All" Button

1. Navigate to `/print-queue`
2. Click on a task to open the View Task dialog
3. Click the "Update All" button to modify product names
4. Your changes are now protected from being overwritten

### Bulk Product Name Updates

For bulk updates, you can:

1. Use the database directly (advanced users)
2. Use the "Update All" button for each product type
3. Create a custom script for specific naming patterns

## Enabling ShipStation Name Updates

### When You Might Want This

- Setting up a new environment
- Correcting a batch of products that have wrong names
- Syncing name changes for specific products from ShipStation

### How to Enable

```bash
# One-time sync with name updates enabled
npm run sync-orders -- --update-product-names

# With specific time range
npm run sync-orders -- --mode recent --hours 24 --update-product-names

# For a specific order
npm run sync-orders -- --mode single --order-id 123456 --update-product-names

# Dry run to see what would change
npm run sync-orders -- --mode recent --update-product-names --dry-run
```

### Workflow Script Modification

If you need to enable name updates in the automated workflow:

```bash
# Edit the workflow script to add the flag
# This is NOT recommended for production use
./scripts/workflow.sh

# Or run sync-orders separately with the flag
npm run sync-orders -- --mode recent --update-product-names
```

## Technical Implementation

### Database Schema

Product names are stored in the `Product` table:

```sql
CREATE TABLE Product (
  id INT PRIMARY KEY,
  name VARCHAR(255),  -- This is the source of truth
  sku VARCHAR(255),
  -- other fields...
);
```

### Code Flow

1. **New Product Creation**: `mapSsItemToProductData()` includes name for new products
2. **Existing Product Updates**: `mapSsItemToProductData()` excludes name unless `updateNames: true`
3. **Flag Control**: `--update-product-names` controls the `updateNames` parameter

### Key Files

- `src/lib/shipstation/mappers.ts` - Product data mapping logic
- `src/lib/shipstation/db-sync.ts` - Database upsert operations
- `src/scripts/sync-orders.ts` - CLI interface with flags
- `src/lib/shipstation/types.ts` - Type definitions for sync options

## Best Practices

### ✅ Recommended Approach

1. **Let new products get names from ShipStation** (automatic)
2. **Manually correct names using "Update All"** when needed
3. **Keep the default behavior** (no `--update-product-names` flag)
4. **Use the flag sparingly** for specific situations

### ❌ Avoid These Patterns

1. **Don't enable `--update-product-names` in crontab** - This will overwrite manual changes
2. **Don't rely on ShipStation for clean names** - Marketplace names are often messy
3. **Don't manually edit the database** unless you understand the implications

## Troubleshooting

### Problem: Manual changes keep getting overwritten

**Solution**: Check if `--update-product-names` is enabled in your crontab or workflow scripts. Remove the flag to preserve manual changes.

### Problem: New products have messy names

**Solution**: This is expected. Use the "Update All" button to clean up names after products are created.

### Problem: Need to update many product names from ShipStation

**Solution**: Use the `--update-product-names` flag for a one-time sync, then return to the default behavior.

## Migration Notes

### Before This Change

- Product names were automatically updated from ShipStation
- Manual changes were frequently overwritten
- No control over when names should be updated

### After This Change

- Product names are preserved by default
- Manual changes are protected
- Explicit flag required to enable name updates
- Better control over product name management

## Related Documentation

- [ShipStation Integration](../integrations/shipstation.md)
- [Sync Orders Script](../scripts/workflows/sync-orders.ts.md)
- [Workflow Script](../scripts/workflow.sh.md)
- [Print Queue System](../print-queue-system.md)
