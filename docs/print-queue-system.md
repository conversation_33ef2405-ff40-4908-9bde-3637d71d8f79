---
title: Print Queue System
last-reviewed: 2025-05-13
maintainer: Y3DHub Team
---

# Print Queue System

This document describes how Y3DHub processes orders from ShipStation into personalized print tasks, manages the print queue, and renders 3D models for printing.

## 1. ShipStation Synchronization
• **Scripts:** `src/scripts/sync-orders.ts` (CLI entry), `src/lib/shared/shipstation.ts` (re-exports),
  `src/lib/shipstation/api.ts` (REST client), `src/lib/shipstation/db-sync.ts` (Prisma upserts)
• **Flow:**
  1. Fetch orders from ShipStation (paginated or single) via Axios client.
  2. Upsert Customers, Orders, OrderItems, Products into MySQL using Prisma.
  3. Store raw `print_settings` JSON on each `OrderItem`.
• **Product Name Handling:**
  - **New Products:** Get initial names from ShipStation when first created.
  - **Existing Products:** Names are preserved by default to protect manual changes.
  - **Manual Override:** Use `--update-product-names` flag to enable name updates when needed.
  - **Source of Truth:** Y3DHub product names take precedence over ShipStation names.

## 2. Selecting Orders for Print Tasks
• **Module:** `src/lib/order-processing.ts`
• **Function:** `getOrdersToProcess(db, orderIdentifier?, limit?, forceRecreate?)`
  - Without identifier: filters for `order_status = 'awaiting_shipment'` and items with no existing tasks.
  - With identifier: looks up by DB ID, then ShipStation number, then ShipStation ID.
  - `forceRecreate` bypasses existing-task filter.

## 3. Personalization Extraction
Personalization can come directly (e.g., Amazon URLs) or via AI fallback.
• **Direct Extraction:**
  - `extractCustomizationUrl(item)` parses `item.print_settings` for a `CustomizedURL` key.
  - `extractDirectItemData(order, item, product)` calls `fetchAndProcessAmazonCustomization(url)` when marketplace=Amazon.
• **AI Fallback:**
  - `extractOrderPersonalization(order, options)` builds a prompt JSON, sends to OpenAI, and validates with Zod schemas (`AiOrderResponseSchema`).
  - Extracts per-item `customText`, `color1`, `color2`, `needsReview`, `annotation`.

## 4. Task Creation
• **Script:** `src/scripts/populate-print-queue.ts`
• **Process:**
  1. Load CLI flags (`--order-id`, `--limit`, `--dryRun`, `--skipAi`, etc.) and prompts.
  2. Call `getOrdersToProcess(...)` to fetch target orders.
  3. For each item: run direct extraction → if none, run AI → merge results.
  4. In a Prisma transaction, insert one or more `PrintOrderTask` rows per item:
     - Fields: `orderId`, `orderItemId`, `productId`, `custom_text`, `color_1`, `color_2`, `quantity`, `status = pending`, `taskIndex`, `annotation`, `needs_review`.
  5. Optionally update ShipStation item options via `updateOrderItemsOptionsBatch(...)` with a truncated details string.

## 5. Rendering Pipeline
• **Worker:** `src/workers/stl-render-worker.ts`
• **Flow:**
  1. Poll for `PrintOrderTask` where `stl_render_state = pending` and `render_retries < MAX_RETRIES`.
  2. Reserve tasks in a transaction (mark as in-progress).
  3. For each task: run OpenSCAD (`renderScadToStl`) with parameters from the product and personalization.
  4. Update task row with `stl_path`, set `stl_render_state = completed` or `error` on failure.
  5. (Optional) Upload to Google Drive and set `gdrive_file_id`, `gdrive_public_link`.

## 6. UI & API
• **Next.js API:**
  - GET `/api/print-tasks?status=pending` (`src/app/api/print-tasks/route.ts`) returns tasks with relations.
  - PATCH `/api/print-tasks/[taskId]/status` (`.../status/route.ts`) updates a single task's status.
• **React Components:**
  - `src/components/print-queue-table.tsx`, `print-queue-filters.tsx`, `print-queue-summary.tsx`, and related modals.
  - `src/app/print-queue/` pages and client wrappers for real-time queue management.
  - The print plan page (`src/app/print-queue/print-plan.tsx`) loads pending tasks directly via Prisma before generating a plan.

## 7. Maintenance & Helper Scripts
• **Mark Completed:** `src/scripts/complete-shipped-print-tasks.ts` updates tasks when orders ship.
• **Cleanup:** `cleanup-shipped-tasks.ts`, `update-discrepant-tasks.ts`, `reprocess-amazon-colors.ts`, etc.
• **Utilities:** SQL fixes (`delete-tasks.sql`), status corrections, retry scripts.

## Key Files & Folders
```
src/scripts/                # CLI scripts for sync, queue, maintenance
src/lib/order-processing.ts # Order filtering for print tasks
src/scripts/populate-print-queue.ts  # Task creation & personalization
src/workers/stl-render-worker.ts     # STL rendering worker
src/app/api/print-tasks/             # API endpoints for queue
src/components/print-queue-*.tsx     # UI components
prisma/schema.prisma                  # Data model for PrintOrderTask, Order, OrderItem
```

Refer to individual files for detailed code comments and error handling logic.