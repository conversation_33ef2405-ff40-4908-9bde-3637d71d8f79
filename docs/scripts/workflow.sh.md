# `scripts/workflow.sh`

* **Purpose:** This script serves as the central automation pipeline for Y3DHub, orchestrating the three core operational steps of the platform's workflow: syncing orders from ShipStation, creating print tasks for those orders, and updating the status of tasks for shipped orders. It features automatic environment variable loading, concurrency control, and extensive command-line options to support various execution modes.

* **Execution:**
    The script is run from the command line, typically from the project root:

    ```bash
    ./scripts/workflow.sh [options]
    ```

    Common usage patterns:

    ```bash
    # Run the standard workflow with default settings (recent orders)
    ./scripts/workflow.sh

    # Process all orders from the last 3 days
    ./scripts/workflow.sh --days-back 3

    # Process only orders from the last 6 hours
    ./scripts/workflow.sh --hours 6

    # Process a specific order
    ./scripts/workflow.sh --mode single --order-id 12345

    # Dry run to see what would happen without making changes
    ./scripts/workflow.sh --dry-run --verbose
    ```

* **Command-line Options:**
  * `--mode <all|recent|single>`: Specifies the sync mode. Default: `recent`
    * `all`: Process all orders from ShipStation
    * `recent`: Process recent orders (controlled by `--days-back` or `--hours`)
    * `single`: Process a single order (requires `--order-id`)
  * `--order-id <ID>`: Process only the specified order. Required when `--mode single` is used.
  * `--days-back <N>`: Sync orders from the last N days. Default: `2`
  * `--hours <N>`: Alternative to `--days-back`. Sync orders from the last N hours.
  * `--dry-run`: Run in simulation mode without making database/API changes.
  * `--verbose`: Enable detailed logging.
  * `--skip-tags`: Skip tag-based processing during ShipStation sync.

* **Key Features & Logic:**
    1. **Environment Setup:**
        * Loads environment variables from `.env` file if present.
        * Validates required ShipStation credentials.
        * Processes command-line arguments.
    2. **Concurrency Control:**
        * Uses `flock` to ensure only one instance of the workflow runs at a time.
        * Creates a lock file at `/tmp/y3dhub_workflow.lock`.
        * Exits with a message if another workflow instance is already running.
        * Uses a trap to ensure the lock file is removed on exit.
    3. **Multi-Step Workflow Execution:**
        * **Step 1:** Syncs orders from ShipStation using `src/scripts/sync-orders.ts`.
            * **Product Names:** By default, preserves existing product names to protect manual changes made via the "Update All" button.
            * **New Products:** Still creates new products with names from ShipStation.
        * **Step 2:** Creates print tasks using `src/scripts/populate-print-queue.ts`.
            * For a specific order ID: Passes `--order-id`.
            * For time-based sync: Converts hours to days (rounding up) for `populate-print-queue.ts`.
            * As fallback: Uses `--limit 30` to process the 30 most recent orders.
        * **Step 3:** Updates the status of print tasks for shipped orders using `src/scripts/complete-shipped-print-tasks.ts`.
    4. **Error Handling:**
        * Uses `set -euo pipefail` for robust error handling.
        * Includes a helper function `log_and_exit_on_error` that checks the exit code of each step.
        * Exits with an informative error message if any step fails.

* **Prerequisites/Dependencies:**
  * Bash shell.
  * `flock` utility (commonly available on Linux systems).
  * Node.js environment with `npx` and `tsx`.
  * The three core scripts must exist and be executable via `tsx`:
    * `src/scripts/sync-orders.ts`
    * `src/scripts/populate-print-queue.ts`
    * `src/scripts/complete-shipped-print-tasks.ts`
  * ShipStation API credentials (`SHIPSTATION_API_KEY` and `SHIPSTATION_API_SECRET`) must be set in the environment or `.env` file.

* **Notes:**
  * This script is the primary operational workflow for Y3DHub, typically executed by a scheduler (cron) at regular intervals.
  * The workflow handles all major aspects of order processing: sync, print task creation, and status updates.
  * The step parameters are carefully constructed to pass appropriate arguments to each underlying script.
  * There's a special time-parameter conversion between `--hours` (accepted by this script) and `--days` (required by `populate-print-queue.ts`).
  * The script was modified to handle `--days-back` vs `--hours` properly after an issue in production where `populate-print-queue.ts` was receiving `--hours` from an older version but expected `--days`.
  * Maintenance/cleanup tasks are noted as being handled outside this script via separate cron jobs.
