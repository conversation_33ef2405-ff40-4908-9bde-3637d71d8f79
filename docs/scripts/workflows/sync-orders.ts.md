# sync-orders.ts Documentation

## Overview

The `sync-orders.ts` script is a crucial part of Y3DHub's operations, responsible for synchronizing order data from ShipStation into the local Y3DHub database. It offers various modes and options to control the synchronization scope and behavior, facilitating both regular updates and specific data retrieval tasks.

This script leverages functions from `src/lib/shared/shipstation` to interact with the ShipStation API and process order data.

## Execution

The script is executed using `tsx` and accepts several command-line arguments to define its operation mode and parameters.

```bash
# General usage
npx tsx src/scripts/sync-orders.ts --mode <sync_mode> [options]

# Example: Sync recent orders from the last 3 days
npx tsx src/scripts/sync-orders.ts --mode recent --days 3

# Example: Sync a single specific order
npx tsx src/scripts/sync-orders.ts --mode single --order-id 123456789

# Example: Perform a dry run for a full sync
npx tsx src/scripts/sync-orders.ts --mode all --dry-run

# Example: Sync recent orders and update product names from ShipStation
npx tsx src/scripts/sync-orders.ts --mode recent --update-product-names
```

Using `run-with-env.sh` (if environment variables are primarily in `.env`):

```bash
./scripts/utils/run-with-env.sh tsx src/scripts/sync-orders.ts --mode recent
```

## Command-Line Options

| Option             | Alias(es) | Type    | Description                                                                 | Default    |
| ------------------ | --------- | ------- | --------------------------------------------------------------------------- | ---------- |
| `--mode`           | `-m`      | string  | Sync mode: `all`, `recent`, `tags`, `single`.                             | `recent`   |
| `--order-id`       | `-o`      | string  | ShipStation Order ID to sync (only used with `mode=single`).              | N/A        |
| `--force-start-date` | `-f`      | string  | Force sync to start from this date (YYYY-MM-DD or ISO format).              | N/A        |
| `--lookback-days`  | `-d`, `--days` | number  | Number of days to look back for recent orders.                              | `2`        |
| `--hours`          | `-h`      | number  | Number of hours to look back for recent orders (overrides `--lookback-days`). | N/A        |
| `--batch-size`     | `-b`      | number  | (Currently unused in script logic) Number of orders per batch.            | `100`      |
| `--skip-tags`      |           | boolean | Skip ShipStation tag synchronization.                                       | `false`    |
| `--dry-run`        |           | boolean | Run the sync without making any database changes.                           | `false`    |
| `--update-product-names` |     | boolean | Allow updating product names from ShipStation (default: false to preserve manual changes). | `false`    |
| `--help`           |           |         | Show help information.                                                      |            |

## Modes of Operation

- **`recent` (Default)**: Synchronizes orders that have been recently modified or created. The lookback period is defined by `--lookback-days` (default 2 days) or `--hours`.
- **`all`**: Performs a full synchronization of all orders from ShipStation. Can be time-consuming. The `--force-start-date` option can be used to specify a custom start date for the historical sync.
- **`single`**: Synchronizes a single, specific order identified by `--order-id`.
- **`tags`**: Only synchronizes ShipStation tags. No orders are processed in this mode. The script will exit after tag synchronization.

## Key Features

- **Multiple Sync Modes**: Tailors data synchronization to specific needs (full, recent, single order, tags only).
- **Tag Synchronization**: Keeps local ShipStation tags up-to-date, which can be crucial for order processing logic. This can be skipped using `--skip-tags`.
- **Date Range Control**: Allows specifying how far back to look for recent orders (`--lookback-days`, `--hours`) or a specific start date for full syncs (`--force-start-date`).
- **Product Name Management**: By default, preserves existing product names to protect manual changes made via the "Update All" button. Use `--update-product-names` to enable name updates from ShipStation when needed.
- **Dry Run Capability**: The `--dry-run` option allows testing the synchronization process without making any actual changes to the database, useful for verification and debugging.
- **Error Handling**: Provides logging for successful operations and errors encountered during the sync process.

## Dependencies

- **ShipStation API**: Relies on API credentials (API Key, API Secret) configured in environment variables for authentication with ShipStation.
- **Internal Libraries**: Uses functions from `src/lib/shared/shipstation.ts` for API interaction and data processing, and `src/lib/shared/logging.ts` for logging.

## Output

- Logs the start and end of the sync process, including the duration.
- Logs the current sync mode and any specific parameters (e.g., order ID, lookback period).
- Outputs status messages for tag synchronization.
- For order syncs, logs the number of orders processed and failed (if applicable).
- In case of errors, logs detailed error messages.
- If `--dry-run` is enabled, a warning message is logged at the beginning.

## Prerequisites

- Node.js and npm/yarn installed.
- `tsx` installed (`npm install -g tsx` or as a project dependency).
- Project dependencies installed (`npm install` or `yarn install`).
- A configured `.env` file (or equivalent environment variable setup) with ShipStation API Key and API Secret, and database connection details.

## Use Cases

- Regularly scheduled (e.g., via cron job) synchronization of recent orders to keep the local database current.
- Performing a one-time full historical sync when setting up a new environment or after a prolonged downtime.
- Syncing a specific order for troubleshooting or immediate processing.
- Updating ShipStation tags independently of order data.
- Verifying sync logic using `--dry-run` before applying changes.

## Notes

- The `batch-size` option is defined but appears to be unused in the current script logic that calls `syncAllPaginatedOrders`, `syncRecentOrders`, and `syncSingleOrder` from `shipstation.ts`. The underlying library functions might handle their own batching or pagination.
- Ensure ShipStation API credentials in the environment are correct and have the necessary permissions.
