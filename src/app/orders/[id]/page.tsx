import { ClockIcon } from '@radix-ui/react-icons';
import { <PERSON>ert<PERSON>ircle, MapPin, User, StickyNote } from 'lucide-react';
import Link from 'next/link';

import { MoreDetailsCard } from '@/components/orders/more-details-card';
import { OrderItemsCard } from '@/components/orders/order-items-card';
import { ShippingInfoCard } from '@/components/orders/shipping-info-card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { CURRENCY_SYMBOL } from '@/lib/constants';
import { formatCountryCode } from '@/lib/formatting';
import { getShippingAlias, getMarketplaceAlias } from '@/lib/mapping-utils';
import { prisma } from '@/lib/prisma';
import { formatDateForTable } from '@/lib/shared/date-utils';
import { cn } from '@/lib/utils';

// TODO: Fix proper typings in a future PR
async function getOrderDetails(
  orderId: string
): Promise<{
  order: any;
  allTags: string[];
}> {
  // Fetch order with all related data
  // Convert orderId string to number for Prisma
  const order = await prisma.order.findUnique({
    where: { id: parseInt(orderId, 10) },
    include: {
      customer: true,
      items: {
        include: {
          printTasks: true,
          product: {
            select: {
              name: true,
              imageUrl: true,
              sku: true,
            },
          },
        },
      },
    },
  });

  if (!order) {
    return { order: null, allTags: [] };
  }

  // Get all available tags for the order tags dropdown
  const tags = await prisma.tag.findMany({
    orderBy: { name: 'asc' },
  });

  const allTags = tags.map((tag) => tag.name);

  // Process and serialize the order data for the client component
  // Using 'as any' type casting to bypass TypeScript checks due to schema/code mismatch
  // TODO: Fix proper typings in a follow-up PR to match the current schema
  const orderAny = order as any;
  const serializedOrder = {
    ...orderAny,
    order_date: orderAny.order_date?.toISOString() || null,
    created_at: orderAny.created_at.toISOString(),
    updated_at: orderAny.updated_at?.toISOString() || null,
    ship_by_date: orderAny.ship_by_date?.toISOString() || null,
    customer: orderAny.customer
      ? {
          ...orderAny.customer,
          created_at: orderAny.customer.created_at.toISOString(),
          updated_at: orderAny.customer.updated_at?.toISOString() || null,
        }
      : null,
    order_items: (orderAny.items || []).map((item: any) => ({
      ...item,
      created_at: item.created_at.toISOString(),
      updated_at: item.updated_at?.toISOString() || null,
      product: item.product ? {
        name: item.product.name,
        imageUrl: item.product.imageUrl,
        sku: item.product.sku,
      } : null,
      print_tasks: (item.printTasks || []).map((task: any) => ({
        ...task,
        created_at: task.created_at.toISOString(),
        updated_at: task.updated_at?.toISOString() || null,
        print_start_time: task.print_start_time?.toISOString() || null,
        print_end_time: task.print_end_time?.toISOString() || null,
        print_progress: task.print_progress
          ? {
              ...task.print_progress,
              created_at: task.print_progress.created_at.toISOString(),
              updated_at: task.print_progress.updated_at?.toISOString() || null,
            }
          : null,
      })),
    })),
    // Adding an empty shipment_address for backward compatibility
    shipment_address: null,
    // Adding empty tags for backward compatibility
    tags: [],
  };

  return { order: serializedOrder, allTags };
}

interface OrderDetailPageProps {
  params: { id: string };
}

// --- Status Color Mappings ---
const orderStatusColors: Record<string, string> = {
  awaiting_shipment: 'bg-blue-500 text-white dark:bg-blue-600 dark:text-blue-100',
  shipped: 'bg-green-600 text-white dark:bg-green-700 dark:text-green-100',
  on_hold: 'bg-yellow-500 text-white dark:bg-yellow-600 dark:text-yellow-100',
  cancelled: 'bg-red-600 text-white dark:bg-red-700 dark:text-red-100',
  default: 'bg-gray-500 text-white dark:bg-gray-600 dark:text-gray-100',
};

const internalStatusColors: Record<string, string> = {
  new: 'bg-sky-500 text-white dark:bg-sky-600 dark:text-sky-100',
  processing: 'bg-purple-500 text-white dark:bg-purple-600 dark:text-purple-100',
};

function getStatusClass(status: string | null | undefined, type: 'order' | 'internal'): string {
  const map = type === 'order' ? orderStatusColors : internalStatusColors;
  const key = status?.toLowerCase() ?? 'default';
  return map[key] || map.default;
}

export default async function OrderDetailPage({ params }: OrderDetailPageProps) {
  const { id } = params;

  // Fetch order data with all related items
  const { order } = await getOrderDetails(id);

  if (!order) {
    return (
      <div className="container mx-auto p-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>Order not found.</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between space-y-2 mb-6">
        <h2 className="text-3xl font-bold tracking-tight">
          Order Details: #{order.shipstation_order_number || order.id}
        </h2>
        <Link href="/orders">
          <Button variant="outline">
            <ClockIcon className="mr-2 h-4 w-4" /> Back to Orders
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Top Row: Order Summary */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <Card className="w-full flex-grow border-l-4 border-orange-500">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 bg-gradient-to-r from-orange-500/30 via-amber-500/30 to-yellow-500/30 dark:from-orange-700/50 dark:via-amber-700/50 dark:to-yellow-700/50 rounded-t-lg px-4 py-3">
              <CardTitle className="text-lg font-semibold">
                Order Summary
              </CardTitle>
              <Badge
                className={cn(
                  'px-3 py-1 text-sm',
                  getStatusClass(order.order_status, 'order'),
                )}
              >
                {order.order_status ? getShippingAlias(order.order_status) : 'Unknown Status'}
              </Badge>
            </CardHeader>
            <CardContent className="pt-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-6 gap-y-3 text-sm">
              <div>
                <p className="text-muted-foreground">Order Date</p>
                <p className="font-medium text-foreground">
                  {order.order_date ? formatDateForTable(order.order_date) : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Ship By</p>
                <p className="font-medium text-foreground">
                  {order.ship_by_date ? formatDateForTable(order.ship_by_date) : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Marketplace</p>
                <p className="font-medium text-foreground">
                  {getMarketplaceAlias(order.marketplace)}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Created</p>
                <p className="font-medium text-foreground">
                  {formatDateForTable(order.created_at)}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Last Updated</p>
                <p className="font-medium text-foreground">
                  {order.updated_at ? formatDateForTable(order.updated_at) : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground">Tags</p>
                {/* TODO: Implement actual tags display and editing */}
                <p className="font-medium text-foreground">-</p>
              </div>
            </CardContent>
            <CardFooter className="bg-muted/20 dark:bg-muted/30 px-4 py-3 rounded-b-lg mt-2">
              <div className="text-lg font-bold text-foreground">
                Total: {CURRENCY_SYMBOL}
                {Number(order.total_price).toFixed(2)}
              </div>
            </CardFooter>
          </Card>

          {/* Customer Information Card */}
          {order.customer && (
            <Card className="border-l-4 border-purple-500">
              <CardHeader className="flex flex-row items-center space-y-0 bg-gradient-to-r from-purple-500/30 via-fuchsia-500/30 to-pink-500/30 dark:from-purple-700/50 dark:via-fuchsia-700/50 dark:to-pink-700/50 rounded-t-lg px-4 py-3">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <User className="h-5 w-5 text-muted-foreground" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm pt-4">
                <p className="font-medium">
                  {order.customer?.name || order.customer_name || 'N/A'}
                </p>
                <div className="text-muted-foreground">
                  <p>Customer ID: {order.customer?.shipstation_customer_id || 'N/A'}</p>
                  <p>Email: {order.customer?.email || 'N/A'}</p>
                  <p>Phone: {order.customer?.phone || 'N/A'}</p>
                </div>
                <div className="flex items-start pt-2">
                  <MapPin className="mr-2 mt-1 h-4 w-4 flex-shrink-0 text-green-600 dark:text-green-400" />
                  <div className="text-muted-foreground">
                    <p className="font-medium text-foreground">Shipping Address</p>
                    {order.customer?.street1 || order.customer?.city ? (
                      <address className="not-italic bg-muted/40 p-2 rounded text-foreground/90 text-sm">
                        {order.customer?.company && (
                          <span>
                            {order.customer.company}
                            <br />
                          </span>
                        )}
                        {order.customer?.street1 && (
                          <span>
                            {order.customer.street1}
                            <br />
                          </span>
                        )}
                        {order.customer?.street2 && (
                          <span>
                            {order.customer.street2}
                            <br />
                          </span>
                        )}
                        {order.customer?.city}, {order.customer?.state}{' '}
                        {order.customer?.postal_code}
                        <br />
                        {formatCountryCode(order.customer?.country_code)}
                      </address>
                    ) : (
                      <p>No address provided</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <ShippingInfoCard order={order} />
        </div>

        {/* Right Column: Order Items, Customer Notes, More Details */}
        <div className="lg:col-span-1 space-y-6">
          <OrderItemsCard items={order.order_items} />

          {/* Customer Notes Section */}
          {order.customer_notes && (
            <Card className="border-l-4 border-rose-500">
              <CardHeader className="flex flex-row items-center space-y-0 bg-gradient-to-r from-rose-500/30 via-red-500/30 to-pink-500/30 dark:from-rose-700/50 dark:via-red-700/50 dark:to-pink-700/50 rounded-t-lg px-4 py-3">
                <CardTitle className="text-lg font-semibold flex items-center gap-2">
                  <StickyNote className="h-5 w-5 text-muted-foreground" />
                  Customer Notes
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                  {order.customer_notes}
                </p>
              </CardContent>
            </Card>
          )}

          {/* More Details Card */}
          <MoreDetailsCard
            className="border-l-4 border-gray-500"
            orderData={{
              payment_date: order.payment_date,
              order_key: order.order_key,
              shipstation_store_id: order.shipstation_store_id,
              payment_method: order.payment_method,
              amount_paid: order.amount_paid,
              shipping_price: order.shipping_price,
              tax_amount: order.tax_amount,
              discount_amount: order.discount_amount,
              shipping_amount_paid: order.shipping_amount_paid,
              shipping_tax: order.shipping_tax,
              gift: order.gift,
              gift_message: order.gift_message,
              internal_notes: order.internal_notes,
              last_sync_date: order.last_sync_date,
              order_weight_value: order.order_weight_value,
              order_weight_units: order.order_weight_units,
              dimensions_units: order.dimensions_units,
              dimensions_length: order.dimensions_length,
              dimensions_width: order.dimensions_width,
              dimensions_height: order.dimensions_height,
              insurance_provider: order.insurance_provider,
              insurance_insure_shipment: order.insurance_insure_shipment,
              insurance_insured_value: order.insurance_insured_value,
              gift_email: order.gift_email,
              notes: order.notes,
            }}
          />
        </div>
      </div>
    </div>
  );
}
