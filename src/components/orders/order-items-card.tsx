import Image from 'next/image';
import { AlertCircle, ShoppingCart } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { CURRENCY_SYMBOL } from '@/lib/constants';
import { cn } from '@/lib/utils';
import type { SerializableOrderItemForDetails } from '@/types/order-details';

const colorMapInternal: { [key: string]: { bg: string; textColor: string } } = {
  black: { bg: 'bg-black', textColor: 'text-white' },
  grey: { bg: 'bg-gray-400', textColor: 'text-white' },
  gray: { bg: 'bg-gray-400', textColor: 'text-white' },
  'light blue': { bg: 'bg-blue-400', textColor: 'text-white' },
  blue: { bg: 'bg-blue-500', textColor: 'text-white' },
  'dark blue': { bg: 'bg-blue-900', textColor: 'text-white' },
  brown: { bg: 'bg-yellow-800', textColor: 'text-white' },
  orange: { bg: 'bg-orange-500', textColor: 'text-white' },
  'matt orange': { bg: 'bg-orange-600', textColor: 'text-white' },
  'silk orange': { bg: 'bg-orange-400', textColor: 'text-black' },
  red: { bg: 'bg-red-600', textColor: 'text-white' },
  'fire engine red': { bg: 'bg-red-700', textColor: 'text-white' },
  'rose gold': { bg: 'bg-pink-300', textColor: 'text-black' },
  magenta: { bg: 'bg-fuchsia-700', textColor: 'text-white' },
  white: { bg: 'bg-white', textColor: 'text-black' },
  'cold white': { bg: 'bg-slate-50 border border-gray-300', textColor: 'text-black' },
  yellow: { bg: 'bg-yellow-400', textColor: 'text-black' },
  silver: { bg: 'bg-gray-300', textColor: 'text-black' },
  'silk silver': { bg: 'bg-gray-200', textColor: 'text-black' },
  purple: { bg: 'bg-purple-500', textColor: 'text-white' },
  pink: { bg: 'bg-pink-400', textColor: 'text-white' },
  'matt pink': { bg: 'bg-pink-500', textColor: 'text-white' },
  'silk pink': { bg: 'bg-pink-300', textColor: 'text-black' },
  gold: { bg: 'bg-yellow-500', textColor: 'text-black' },
  skin: { bg: 'bg-orange-200', textColor: 'text-black' },
  'peak green': { bg: 'bg-green-400', textColor: 'text-white' },
  green: { bg: 'bg-green-500', textColor: 'text-white' },
  'olive green': { bg: 'bg-green-700', textColor: 'text-white' },
  'pine green': { bg: 'bg-green-800', textColor: 'text-white' },
  'glow in the dark': { bg: 'bg-lime-300', textColor: 'text-black' },
  bronze: { bg: 'bg-amber-700', textColor: 'text-white' },
  beige: { bg: 'bg-amber-100', textColor: 'text-black' },
  turquoise: { bg: 'bg-teal-400', textColor: 'text-black' },
};

const getColorInfo = (
  colorName: string | null | undefined,
): { bgClass: string; textClass: string } => {
  const defaultColor = { bgClass: 'bg-gray-200', textClass: 'text-black' };
  if (!colorName) return { bgClass: 'bg-transparent', textClass: 'text-foreground' };
  const lowerColorName = colorName.toLowerCase();
  if (lowerColorName.includes('peak green'))
    return { bgClass: colorMapInternal['peak green'].bg, textClass: colorMapInternal['peak green'].textColor };
  if (lowerColorName.includes('light blue'))
    return { bgClass: colorMapInternal['light blue'].bg, textClass: colorMapInternal['light blue'].textColor };
  if (lowerColorName.includes('dark grey') || lowerColorName.includes('dark gray'))
    return { bgClass: 'bg-gray-600', textClass: 'text-white' };
  if (lowerColorName.includes('magenta'))
    return { bgClass: colorMapInternal.magenta.bg, textClass: colorMapInternal.magenta.textColor };
  if (lowerColorName.includes('white'))
    return { bgClass: colorMapInternal.white.bg, textClass: colorMapInternal.white.textColor };
  const exactMatch = colorMapInternal[lowerColorName];
  if (exactMatch) return { bgClass: exactMatch.bg, textClass: exactMatch.textColor };
  const entries = Object.entries(colorMapInternal).sort((a, b) => b[0].length - a[0].length);
  for (const [key, value] of entries) {
    if (lowerColorName.includes(key)) return { bgClass: value.bg, textClass: value.textColor };
  }
  return defaultColor;
};

interface SerializedPrintTask {
  id: string;
  orderId: number;
  marketplace_order_number: string | null;
  customerId: number | null;
  custom_text: string | null;
  quantity: number;
  color_1: string | null;
  color_2: string | null;
  ship_by_date: string | null;
  status: string;
  needs_review: boolean;
  review_reason: string | null;
  created_at: string;
  updated_at: string | null;
  orderItemId: number;
  taskIndex: number;
  productId: number;
  shorthandProductName: string | null;
}

interface OrderItemsCardProps {
  items: SerializableOrderItemForDetails[];
}

export function OrderItemsCard({ items }: OrderItemsCardProps) {
  return (
    <Card className="border-l-4 border-orange-500">
      <CardHeader className="flex flex-row items-center space-y-0 bg-gradient-to-r from-orange-500/30 via-amber-500/30 to-yellow-500/30 dark:from-orange-700/50 dark:via-amber-700/50 dark:to-yellow-700/50 rounded-t-lg px-4 py-3">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <ShoppingCart className="h-5 w-5 text-muted-foreground" />
          Order Items
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-4">
        {items.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[80px] h-10 px-2 text-xs">Preview</TableHead>
                <TableHead className="h-10 px-2 text-xs">Product</TableHead>
                <TableHead className="h-10 px-2 text-xs">SKU</TableHead>
                <TableHead className="h-10 px-2 text-xs text-right">Qty</TableHead>
                <TableHead className="h-10 px-2 text-xs text-right">Unit Price</TableHead>
                <TableHead className="h-10 px-2 text-xs text-right">Total</TableHead>
                <TableHead className="h-10 px-2 text-xs">Personalization</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item: SerializableOrderItemForDetails) => (
                <TableRow key={item.id}>
                  <TableCell className="p-2 align-top">
                    <div className="w-16 h-16 bg-muted rounded-md overflow-hidden relative border">
                      {item.product?.imageUrl ? (
                        <Image src={item.product.imageUrl} alt={item.product.name || 'Product image'} fill className="object-contain" unoptimized />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">No Image</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="p-2 align-top">
                    <p className="font-semibold text-sm">{item.product?.name || 'Product Name Missing'}</p>
                    <p className="text-xs text-muted-foreground">Weight: {item.product?.weight ? `${item.product.weight}units` : 'N/A'}</p>
                  </TableCell>
                  <TableCell className="p-2 align-top text-xs">{item.product?.sku || 'N/A'}</TableCell>
                  <TableCell className="p-2 align-top text-right">
                    <Badge variant="secondary" className="px-2 py-0.5 text-sm font-semibold">
                      {item.quantity}
                    </Badge>
                  </TableCell>
                  <TableCell className="p-2 align-top text-xs text-right">
                    {CURRENCY_SYMBOL}
                    {Number(item.unit_price).toFixed(2)}
                  </TableCell>
                  <TableCell className="p-2 align-top text-xs text-right font-semibold">
                    {CURRENCY_SYMBOL}
                    {(item.quantity * Number(item.unit_price)).toFixed(2)}
                  </TableCell>
                  <TableCell className="p-2 align-top">
                    {item.printTasks && item.printTasks.length > 0 ? (
                      <Table className="mt-0 -mx-2 -my-1 w-[calc(100%+1rem)]">
                        <TableHeader>
                          <TableRow className="text-xs">
                            <TableHead className="h-7 px-1 py-0.5 text-xs">Task</TableHead>
                            <TableHead className="h-7 px-1 py-0.5 text-xs">Status</TableHead>
                            <TableHead className="h-7 px-1 py-0.5 text-xs">Review?</TableHead>
                            <TableHead className="h-7 px-1 py-0.5 text-xs">Text</TableHead>
                            <TableHead className="h-7 px-1 py-0.5 text-xs">Color1</TableHead>
                            <TableHead className="h-7 px-1 py-0.5 text-xs">Color2</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {item.printTasks.map((task: SerializedPrintTask) => (
                            <TableRow key={task.id} className="text-xs">
                              <TableCell className="px-1 py-0.5 text-[11px] leading-tight">{task.id}</TableCell>
                              <TableCell className="px-1 py-0.5">
                                <Badge variant="outline" className="text-[10px] px-1 py-0 leading-tight border">
                                  {task.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="px-1 py-0.5">
                                {task.needs_review ? (
                                  <Badge variant="destructive" title={task.review_reason ?? ''} className="cursor-help text-[10px] px-1 py-0 leading-tight">
                                    <AlertCircle className="h-2.5 w-2.5 mr-0.5" /> Yes
                                  </Badge>
                                ) : (
                                  <span className="text-muted-foreground text-[11px]">No</span>
                                )}
                              </TableCell>
                              <TableCell className="font-mono max-w-[10ch] truncate px-1 py-0.5 text-[11px] leading-tight" title={task.custom_text ?? ''}>
                                {task.custom_text ?? '-'}
                              </TableCell>
                              <TableCell className="px-1 py-0.5">
                                {task.color_1 ? (
                                  (() => {
                                    const { bgClass, textClass } = getColorInfo(task.color_1);
                                    return (
                                      <span
                                        className={cn(
                                          'px-1.5 py-0.5 rounded text-[10px] font-medium inline-block min-w-[60px] text-center leading-tight',
                                          bgClass,
                                          textClass,
                                          task.color_1.toLowerCase() === 'white' ? 'border border-gray-300' : 'border border-gray-500/50',
                                        )}
                                        title={task.color_1}
                                      >
                                        {task.color_1}
                                      </span>
                                    );
                                  })()
                                ) : (
                                  <span className="text-muted-foreground text-[11px]">-</span>
                                )}
                              </TableCell>
                              <TableCell className="px-1 py-0.5">
                                {task.color_2 ? (
                                  (() => {
                                    const { bgClass, textClass } = getColorInfo(task.color_2);
                                    return (
                                      <span
                                        className={cn(
                                          'px-1.5 py-0.5 rounded text-[10px] font-medium inline-block min-w-[60px] text-center leading-tight',
                                          bgClass,
                                          textClass,
                                          task.color_2.toLowerCase() === 'white' ? 'border border-gray-300' : 'border border-gray-500/50',
                                        )}
                                        title={task.color_2}
                                      >
                                        {task.color_2}
                                      </span>
                                    );
                                  })()
                                ) : (
                                  <span className="text-muted-foreground text-[11px]">-</span>
                                )}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    ) : (
                      <p className="text-xs text-muted-foreground italic py-1">No personalization tasks.</p>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <p className="text-center text-muted-foreground py-4">No items found for this order.</p>
        )}
      </CardContent>
    </Card>
  );
}

