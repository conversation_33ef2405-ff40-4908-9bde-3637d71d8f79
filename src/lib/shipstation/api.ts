import axios from 'axios';
import logger from '@/lib/logger';

import { upsertOrderWithItems } from './db-sync';
import type { ShipStationOrder, ShipStationOrdersResponse } from './types'; // Corrected import
import { shipstation<PERSON><PERSON> } from './client';
import {
  sendSystemNotification,
  ErrorSeverity,
  ErrorCategory,
} from '../email/system-notifications';

import {
  ShipStationApiParams,
  SyncSummary,
  ShipStationSyncProcessParams,
  ShipStationOrderItem, // Ensure this is imported if not already explicitly (it is in the original types import)
  ShipStationItemOption,
} from './types'; // Assuming these types are defined/updated in ./types.ts

// CONSTANTS for dimension conversion
const INCH_TO_CM_FACTOR = 2.54;
const DIMENSION_PRECISION = 2;

const MAX_RETRIES = 3;
const ORDER_RETRY_LIMIT = 3;

/**
 * Fetches orders from the ShipStation API with retry logic and improved error handling.
 * This is a simplified version that focuses on the core functionality.
 * @param params - Query parameters for the ShipStation API
 * @returns The API response data
 * @throws Error if all retry attempts fail
 */
export const fetchOrdersWithRetry = async ( // Renamed from getShipstationOrders in OURS
  params: ShipStationApiParams = {}
): Promise<ShipStationOrdersResponse> => {
  let attempt = 0;

  // --- TEMPORARILY REMOVED DEFAULT DATE FILTER FOR TESTING ---
  // // Default start date if none provided (modifyDateStart is often useful)
  // if (
  //   !params.modifyDateStart &&
  //   !params.createDateStart &&
  //   !params.orderDateStart
  // ) {
  //   const yesterday = new Date()
  //   yesterday.setDate(yesterday.getDate() - 1)
  //   params.modifyDateStart = yesterday.toISOString()
  //   logger.info(`[API] Defaulting to modifyDateStart: ${params.modifyDateStart}`)
  // }
  // --- END REMOVAL ---

  // Ensure pageSize is reasonable, default to 100 (API max might be 500)
  params.pageSize = params.pageSize || 100;

  while (attempt < MAX_RETRIES) {
    attempt++;
    try {
      let response;
      const orderIdToFetch = params.orderId;

      // If a specific orderId is provided, fetch that single order
      if (orderIdToFetch) {
        const endpoint = `/orders/${orderIdToFetch}`;
        logger.info(
          `[API] Attempt ${attempt}: Fetching single order from ShipStation: ${endpoint}`
        );
        // Fetch single order - response data is the ShipStationOrder object directly
        response = await shipstationApi.get<ShipStationOrder>(endpoint);

        // Wrap the single order in the expected list response structure
        const singleOrderData = response.data;
        logger.info(
          `[API] Fetched single order ${singleOrderData.orderNumber} (ID: ${singleOrderData.orderId}). Status: ${singleOrderData.orderStatus}`
        );
        return {
          orders: [singleOrderData],
          total: 1,
          page: 1,
          pages: 1,
        }; // Return wrapped response
      } else {
        // Otherwise, fetch the list of orders using provided params
        logger.info(
          `[API] Attempt ${attempt}: Fetching order list from ShipStation with params:`,
          params
        );
        response = await shipstationApi.get<ShipStationOrdersResponse>(
          '/orders',
          { params } // Pass the original params for filtering/pagination
        );
        logger.info(
          `[API] Fetched ${response.data.orders.length} orders from page ${response.data.page}/${response.data.pages}. Total: ${response.data.total}`
        );
        return response.data; // Return list response
      }
    } catch (error: unknown) {
      let errorMessage = '[API] Error fetching ShipStation orders';
      let statusCode: number | string = 'N/A';
      let shouldRetry = false;

      if (axios.isAxiosError(error)) {
        statusCode = error.response?.status ?? 'N/A';
        errorMessage += `. Status: ${statusCode}.`;
        // Log only essential error info, avoid logging potentially large data object in prod
        logger.error(`${errorMessage} Attempt ${attempt}. URL: ${error.config?.url}`);
        if (error.response?.data) {
          // Log specific error message from ShipStation if available
          logger.error(` -> ShipStation Response: ${JSON.stringify(error.response.data)}`);
        }

        // Retry on common transient errors (rate limits, server errors)
        if (statusCode === 429 || (typeof statusCode === 'number' && statusCode >= 500)) {
          shouldRetry = true;
        }
      } else if (error instanceof Error) {
        errorMessage += `: ${error.message}. Attempt ${attempt}.`;
        logger.error(errorMessage, error);
      } else {
        errorMessage += `: Unknown error occurred. Attempt ${attempt}.`;
        logger.error(errorMessage, error);
      }

      if (shouldRetry && attempt < MAX_RETRIES) {
        const delay = Math.pow(2, attempt) * 1000; // Exponential backoff (2s, 4s)
        logger.info(`[API] Retrying after ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } else {
        // Don't retry or max retries reached
        const finalMessage = `${errorMessage}. Max retries reached or non-retryable error.`;
        logger.error(`[API] Failed to fetch orders after ${attempt} attempts. ${finalMessage}`);
        // Re-throw the error to be caught by the sync orchestrator
        throw new Error(finalMessage, { cause: error });
      }
    }
  }
  // Should not be reachable due to throw in the loop, but satisfies TypeScript
  throw new Error('[API] Unexpected error state after fetch attempts.');
};

/**
 * Synchronizes ShipStation order data with the local database
 * @param syncParams - Synchronization parameters
 * @param forceStartDate - Optional: Forces a specific start date for historical sync, overriding other date params.
 * @returns Summary of the sync operation
 */
export const syncShipstationData = async (
  syncParams: ShipStationSyncProcessParams = {},
  forceStartDate?: string // From THEIRS
): Promise<SyncSummary> => {
  let currentPage = 1;
  let totalPages = 1; // Will be updated based on API response
  let ordersSuccessfullyProcessed = 0;
  let ordersFailedToProcess = 0;
  let totalOrdersFetched = 0;
  let ordersSkippedStatus = 0; // Track skipped orders
  const maxPagesToSync = syncParams.pageLimit ?? Infinity; // Default to Infinity if no limit for historical sync
  const pageSize = syncParams.pageSize ?? 100; // From THEIRS
  let syncFailed = false;
  let failureReason = '';
  const errors: Array<{ orderNumber: string; orderId: string; message: string }> = []; // From THEIRS

  logger.info('Starting ShipStation data synchronization...');

  if (syncParams.dryRun) { // From THEIRS
    logger.info('[DRY RUN] No changes will be made to the database');
  }

  // Prepare API parameters based on sync options
  const apiParams: ShipStationApiParams = {
    pageSize: pageSize,
    sortDir: 'ASC',
  };

  // Check if processing a single order
  if (syncParams.singleOrderId) {
    logger.info(`[SYNC] Single order sync requested for order ID: ${syncParams.singleOrderId}`);
    apiParams.orderId = Number(syncParams.singleOrderId);
    // No need for date filters when fetching a specific order
  } else {
    // Date handling: Combine OURS's date range with THEIRS's forceStartDate
    if (forceStartDate) {
      apiParams.orderDateStart = forceStartDate;
      apiParams.sortBy = 'OrderDate'; // Sort by OrderDate for historical sync
      logger.info(
        `[SYNC] Historical sync initiated with forceStartDate: ${forceStartDate}, sortBy: OrderDate.`
      );
    } else {
      // Default to ModifyDate sort for recent changes, or OrderDate if a range is specified
      apiParams.sortBy = syncParams.orderDateStart || syncParams.orderDateEnd ? 'OrderDate' : 'ModifyDate';
      // Apply modifyDateStart if provided, otherwise default to 24 hours ago
      apiParams.modifyDateStart = syncParams.modifyDateStart || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      logger.info(
        `[SYNC] Delta sync initiated with modifyDateStart: ${apiParams.modifyDateStart}, sortBy: ${apiParams.sortBy}.`
      );
    }

    // Add orderDateEnd if provided in syncParams (relevant for historical syncs over a range)
    if (syncParams.orderDateEnd) {
      apiParams.orderDateEnd = syncParams.orderDateEnd;
      logger.info(`[SYNC] orderDateEnd applied: ${syncParams.orderDateEnd}`);
    }
  }

  // Log parameters being used
  logger.info('[Sync] Effective API Parameters:', JSON.stringify(apiParams, null, 2));
  logger.info(`[Sync] Page limit set to: ${maxPagesToSync === Infinity ? 'None' : maxPagesToSync}`);

  try {
    do { // Adopt do-while from OURS
      logger.info(
        `[Sync] Syncing page ${currentPage} of ${totalPages === Infinity ? '?' : totalPages}...`
      );
      // Use the unified fetchOrdersWithRetry
      const response = await fetchOrdersWithRetry({ ...apiParams, page: currentPage });

      if (!response || !response.orders) {
        throw new Error(
          `[Sync] Inconsistent state: Failed to get orders response for page ${currentPage}, but no error thrown.`
        );
      }

      if (currentPage === 1) {
        totalPages = response.pages || Infinity; // Handle potential API differences
        logger.info(`[Sync] Total pages reported by API: ${totalPages}`);
      }

      if (response.orders.length === 0) {
        logger.info(`[Sync] No more orders found on page ${currentPage}. Ending sync.`);
        break; // Exit loop if API returns empty page
      }

      totalOrdersFetched += response.orders.length;
      logger.info(
        `[Sync] Processing ${response.orders.length} orders from page ${currentPage}/${totalPages === Infinity ? '?' : totalPages}...`
      );

      // Filter fetched orders locally for desired statuses BEFORE processing (from OURS)
      const ordersToProcess = syncParams.syncAllStatuses
        ? response.orders
        : response.orders.filter(ssOrder => {
            const shouldProcess = ssOrder.orderStatus === 'awaiting_shipment' || ssOrder.orderStatus === 'on_hold';
            if (!shouldProcess) {
              ordersSkippedStatus++;
              // Optional: Log skipped orders if needed for debugging
              // logger.info(`[Sync] Skipping order ${ssOrder.orderNumber} (Status: ${ssOrder.orderStatus})`);
            }
            return shouldProcess;
          });

      logger.info(
        `[Sync] -> Filtered to ${ordersToProcess.length} orders${!syncParams.syncAllStatuses ? ' with status \'awaiting_shipment\' or \'on_hold\'' : ''}.`
      );

      if (!syncParams.dryRun) { // From THEIRS
        for (const ssOrder of ordersToProcess) {
          let attempt = 0;
          let processed = false;
          let lastError: unknown = null;

          while (attempt < ORDER_RETRY_LIMIT && !processed) {
            attempt++;
            try {
              // Pass dryRun and updateProductNames to upsertOrderWithItems (from THEIRS)
              const result = await upsertOrderWithItems(ssOrder, {
                dryRun: syncParams.dryRun,
                updateProductNames: syncParams.updateProductNames
              });
              if (result?.success) {
                ordersSuccessfullyProcessed++;
                processed = true;
              } else {
                lastError = new Error('Upsert returned unsuccessful');
                if (attempt < ORDER_RETRY_LIMIT) {
                  logger.warn(
                    `[Sync] Order ${ssOrder.orderNumber} upsert failed (attempt ${attempt}). Retrying...`
                  );
                }
              }
            } catch (itemProcessingError) {
              lastError = itemProcessingError;
              logger.error(
                `[Sync] Error processing order ${ssOrder.orderNumber} attempt ${attempt}:`,
                itemProcessingError
              );
              if (attempt < ORDER_RETRY_LIMIT) {
                logger.warn(
                  `[Sync] Retrying order ${ssOrder.orderNumber} (${attempt}/${ORDER_RETRY_LIMIT})`
                );
              }
            }
          }

          if (!processed) {
            ordersFailedToProcess++;
            syncFailed = true; // Mark overall sync as failed
            failureReason = `Failed to process order ${ssOrder.orderNumber}`;
            errors.push({ // Add to errors array (from THEIRS)
              orderNumber: ssOrder.orderNumber,
              orderId: ssOrder.orderId?.toString() || 'N/A',
              message: `Failed after ${ORDER_RETRY_LIMIT} attempts: ${lastError instanceof Error ? lastError.message : String(lastError)}`,
            });
            await sendSystemNotification(
              'Order Processing Failed',
              `Order ${ssOrder.orderNumber} failed after ${ORDER_RETRY_LIMIT} attempts`,
              ErrorSeverity.ERROR,
              ErrorCategory.SYNC,
              lastError
            );
          }
        }
      } else { // DRY RUN logic from THEIRS
        logger.info(`[DRY RUN] Would process ${ordersToProcess.length} orders from page ${currentPage}`);
        ordersSuccessfullyProcessed += ordersToProcess.length; // In dry run, assume success
      }

      // Update progress
      logger.info(
        `[Sync] Page ${currentPage}/${totalPages} processed. ` +
        `Success: ${ordersSuccessfullyProcessed}, ` +
        `Failed: ${ordersFailedToProcess}, ` +
        `Skipped (status): ${ordersSkippedStatus}`
      );

      currentPage++;
      // Avoid infinite loops if totalPages is weird; rely on empty page break
    } while (currentPage <= totalPages && currentPage <= maxPagesToSync); // Adopt do-while condition from OURS

    // Build summary message
    const message = syncFailed
      ? `ShipStation sync COMPLETED WITH ERRORS: ${failureReason}`
      : syncParams.dryRun // From THEIRS
      ? 'ShipStation sync dry run completed successfully'
      : 'ShipStation sync completed successfully';

    logger.info(`[Sync] ${message}`, {
      ordersProcessed: ordersSuccessfullyProcessed,
      ordersFailed: ordersFailedToProcess,
      ordersSkipped: ordersSkippedStatus,
      totalOrdersFetched,
      pagesSynced: currentPage - 1, // Number of pages actually fetched
      totalPagesAvailable: totalPages === Infinity ? 'Unknown' : totalPages,
      dryRun: syncParams.dryRun || false, // From THEIRS
    });

    if (syncFailed) {
      await sendSystemNotification(
        'ShipStation Sync Completed With Errors',
        failureReason,
        ErrorSeverity.ERROR,
        ErrorCategory.SYNC
      );
    }

    return {
      success: !syncFailed,
      message,
      ordersProcessed: ordersSuccessfullyProcessed,
      ordersFailed: ordersFailedToProcess,
      ordersSkipped: ordersSkippedStatus,
      totalOrdersFetched,
      pagesSynced: currentPage - 1,
      totalPagesAvailable: totalPages === Infinity ? -1 : totalPages,
      dryRun: syncParams.dryRun, // From THEIRS
      errors: errors, // From THEIRS
    };
  } catch (error: unknown) {
    logger.error('[Sync] Critical error during ShipStation sync:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    syncFailed = true;
    failureReason = errorMessage;

    await sendSystemNotification(
      'ShipStation Sync Failed',
      errorMessage,
      ErrorSeverity.CRITICAL,
      ErrorCategory.SYNC,
      error
    );

    return {
      success: false,
      message: `ShipStation sync FAILED: ${errorMessage}`,
      ordersProcessed: ordersSuccessfullyProcessed,
      ordersFailed: ordersFailedToProcess +
                   (totalOrdersFetched - ordersSuccessfullyProcessed - ordersSkippedStatus),
      ordersSkipped: ordersSkippedStatus,
      totalOrdersFetched,
      pagesSynced: currentPage - 1,
      totalPagesAvailable: totalPages === Infinity ? -1 : totalPages,
      dryRun: syncParams.dryRun, // From THEIRS
      errors: errors, // From THEIRS
    };
  }
};

// Alias for backward compatibility (from THEIRS)
export const syncAllPaginatedOrders = syncShipstationData;

/**
 * Updates the options for a specific order item in ShipStation.
 * Uses the /orders/createorder endpoint which also handles updates.
 * @param lineItemKey The unique key for the order item to update.
 * @param newOptions An array of option objects ({ name: string, value: string | null }).
 * @param fetchedOrder The full fetched order object.
 * @returns {Promise<boolean>} True if successful, false otherwise.
 */
export async function updateOrderItemOptions( // From OURS, exported
  lineItemKey: string,
  newOptions: Array<{ name: string; value: string | null }>,
  fetchedOrder: ShipStationOrder // Accept the full fetched order object
): Promise<boolean> {
  const endpoint = '/orders/createorder';

  // Map over the fetched items to create the updated items array
  const updatedOrderItems = fetchedOrder.items.map((item: ShipStationOrderItem) => {
    if (item.lineItemKey === lineItemKey) {
      // Return the target item with updated options
      // Keep all original item fields, just override options
      return {
        ...item,
        options: newOptions.filter(opt => opt.value !== null), // Set the new options
      };
    }
    // Return other items unchanged
    return item;
  });

  // Construct payload by spreading the fetched order and overriding items
  const payload = {
    ...fetchedOrder, // Spread all properties from the fetched order
    items: updatedOrderItems, // Override with the modified items array
  };

  // Convert dimensions if they exist and are in inches
  if (
    payload.dimensions &&
    payload.dimensions.units &&
    payload.dimensions.units.toLowerCase() === 'inches' &&
    typeof payload.dimensions.length === 'number' &&
    typeof payload.dimensions.width === 'number' &&
    typeof payload.dimensions.height === 'number'
  ) {
    logger.info(
      `[ShipStation API][Order ${payload.orderId}] Converting dimensions from inches to cm and scaling values for batch update.`
    );
    payload.dimensions = {
      units: 'cm',
      length: parseFloat(
        (payload.dimensions.length * INCH_TO_CM_FACTOR).toFixed(
          DIMENSION_PRECISION
        )
      ),
      width: parseFloat(
        (payload.dimensions.width * INCH_TO_CM_FACTOR).toFixed(
          DIMENSION_PRECISION
        )
      ),
      height: parseFloat(
        (payload.dimensions.height * INCH_TO_CM_FACTOR).toFixed(
          DIMENSION_PRECISION
        )
      ),
    };
  } else if (
    payload.dimensions &&
    payload.dimensions.units &&
    payload.dimensions.units.toLowerCase() === 'inches'
  ) {
    // If conversion can't happen due to missing numeric properties but units are inches, set to null to avoid sending invalid partial data
    logger.warn(
      `[ShipStation API][Order ${payload.orderId}] Original dimensions in inches but one or more numeric dimension properties (length, width, height) are missing or not numbers. Setting dimensions to null for the API call.`
    );
    payload.dimensions = null;
  }

  // Ensure orderId is a number if it exists (it should)
  if (payload.orderId) {
    payload.orderId = Number(payload.orderId);
  }

  logger.info(
    `[ShipStation API] Updating options for item ${lineItemKey} in order ${fetchedOrder.orderId} (Order Number: ${fetchedOrder.orderNumber})...`
  );
  try {
    logger.debug('[ShipStation API] Sending payload:', JSON.stringify(payload, null, 2));
    const response = await shipstationApi.post(endpoint, payload);
    if (response.status === 200 || response.status === 201) {
      logger.info(
        `[ShipStation API] Successfully updated options for item ${lineItemKey} in order ${fetchedOrder.orderId}.`
      );
      return true;
    } else {
      logger.warn(
        `[ShipStation API] Unexpected status code ${response.status} when updating item options for order ${fetchedOrder.orderId}.`
      );
      return false;
    }
  } catch (error: unknown) {
    let errorMessage = `[ShipStation API] Error updating item options for item ${lineItemKey} in order ${fetchedOrder.orderId}`;
    if (axios.isAxiosError(error)) {
      errorMessage += `. Status: ${error.response?.status ?? 'N/A'}`;
      if (error.response?.data) {
        errorMessage += ` Response: ${JSON.stringify(error.response.data)}`;
      }
    } else if (error instanceof Error) {
      errorMessage += `: ${error.message}`;
    }
    logger.error(errorMessage, error);
    return false;
  }
}

export async function updateOrderItemsOptionsBatch( // From OURS, exported
  fetchedOrder: ShipStationOrder,
  itemsToPatch: Record<string, Array<{ name: string; value: string | null }>>,
  auditNote: string | null = null,
  dimensionsInput: { units: string; length: number; width: number; height: number } | null = null,
  customerNotes?: string | null
): Promise<boolean> {
  const endpoint = '/orders/createorder';

  // Define constants and helper function locally within this function scope
  const SHIPSTATION_INTERNAL_NOTES_MAX_LENGTH = 10000;
  const SHIPSTATION_INTERNAL_NOTES_TRUNCATION_SUFFIX = '... (truncated)';

  function sanitizeAndTruncateShipstationInternalNotes(notes: string): string {
    // Strip non-printable ASCII
    // eslint-disable-next-line no-control-regex
    let sanitizedNotes = notes.replace(/[^\x20-\x7E\x09\x0A\x0D]/g, '');
    if (sanitizedNotes.length > SHIPSTATION_INTERNAL_NOTES_MAX_LENGTH) {
      const maxLengthWithoutSuffix = SHIPSTATION_INTERNAL_NOTES_MAX_LENGTH - SHIPSTATION_INTERNAL_NOTES_TRUNCATION_SUFFIX.length;
      if (maxLengthWithoutSuffix < 0) {
        sanitizedNotes = sanitizedNotes.substring(0, SHIPSTATION_INTERNAL_NOTES_MAX_LENGTH);
      } else {
        sanitizedNotes = sanitizedNotes.substring(0, maxLengthWithoutSuffix) + SHIPSTATION_INTERNAL_NOTES_TRUNCATION_SUFFIX;
      }
      logger.warn(
        `[ShipStation API][Order ${fetchedOrder.orderId}] Internal notes truncated to ${SHIPSTATION_INTERNAL_NOTES_MAX_LENGTH} characters.`
      );
    }
    return sanitizedNotes;
  }

  let freshlyFetchedOrder: ShipStationOrder;
  try {
    logger.info(`[ShipStation API] Fetching latest order data for orderId ${fetchedOrder.orderId} before batch update.`);
    // Use the unified fetchOrdersWithRetry
    const response = await fetchOrdersWithRetry({ orderId: fetchedOrder.orderId });
    if (!response.orders || response.orders.length === 0) {
      logger.error(`[ShipStation API] Failed to fetch fresh order data for orderId ${fetchedOrder.orderId}. Aborting update.`);
      return false;
    }
    freshlyFetchedOrder = response.orders[0];
    logger.info(
      `[ShipStation API - DEBUG] Original FRESHLY fetched order data for orderId ${freshlyFetchedOrder.orderId} BEFORE any modification:\n ${JSON.stringify(freshlyFetchedOrder, null, 2)}`
    );
  } catch (fetchError) {
    logger.error(`[ShipStation API] Error fetching fresh order data for orderId ${fetchedOrder.orderId}: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}. Aborting update.`);
    return false;
  }

  // Map itemsToPatch to the structure ShipStation expects, using freshlyFetchedOrder's items as a base
  const updatedOrderItems = freshlyFetchedOrder.items.map((item: ShipStationOrderItem) => {
    const patchOptions = itemsToPatch[item.lineItemKey || ''];
    if (patchOptions) {
      // If new options are provided for this lineItemKey, process them to ensure compatibility
      const SSpatchOptions: ShipStationItemOption[] = patchOptions
        .map(opt => ({
          name: opt.name,
          value: opt.value === null ? "" : opt.value, // Convert null to empty string
        }))
      return { ...item, options: SSpatchOptions };
    }
    // Otherwise, keep the item as it is from the fresh fetch
    return item;
  });

  // Sanitize internal notes before sending
  const sanitizedAuditNote = auditNote ? sanitizeAndTruncateShipstationInternalNotes(auditNote) : null;

  // Construct payload by spreading the FRESHLY fetched order and overriding specific fields
  const payload: ShipStationOrder = { // Using ShipStationOrder type
    ...freshlyFetchedOrder,
    items: updatedOrderItems,
    internalNotes: sanitizedAuditNote, // Use sanitized notes
    customerNotes: null, // Initialize customerNotes, will be set below
  };

  // Handle customerNotes: if provided and not empty, add to payload; otherwise, send null to potentially clear it.
  if (customerNotes && customerNotes.trim() !== "") {
    payload.customerNotes = customerNotes;
  } else {
    payload.customerNotes = null; // Send null to clear or if no customer notes are intended
  }

  // Dimension handling (using dimensionsInput, which is the 'dimensions' from the calling function)
  // This logic assumes 'dimensionsInput' is what the CALLER wants to set.
  // It might override freshlyFetchedOrder.dimensions or convert them.

  if (dimensionsInput !== undefined) { // Check if dimensionsInput was explicitly passed
    payload.dimensions = dimensionsInput; // Directly use the input from the function call

    // Apply conversion if units are in inches and numeric properties are valid on dimensionsInput
    if (
      payload.dimensions && // Ensure dimensions is not null from the assignment above
      payload.dimensions.units &&
      payload.dimensions.units.toLowerCase() === 'inches' &&
      typeof payload.dimensions.length === 'number' &&
      typeof payload.dimensions.width === 'number' &&
      typeof payload.dimensions.height === 'number'
    ) {
      const scaledLength = payload.dimensions.length * INCH_TO_CM_FACTOR;
      const scaledWidth = payload.dimensions.width * INCH_TO_CM_FACTOR;
      const scaledHeight = payload.dimensions.height * INCH_TO_CM_FACTOR;

      // Check if any converted dimension becomes effectively zero after precision
      if (
        parseFloat(scaledLength.toFixed(DIMENSION_PRECISION)) === 0.00 ||
        parseFloat(scaledWidth.toFixed(DIMENSION_PRECISION)) === 0.00 ||
        parseFloat(scaledHeight.toFixed(DIMENSION_PRECISION)) === 0.00
      ) {
        logger.warn(
          `[ShipStation API][Order ${payload.orderId}] Original Inch Dimensions: L ${payload.dimensions.length}, W ${payload.dimensions.width}, H ${payload.dimensions.height}. At least one converted dimension is zero after precision, sending null for dimensions.`
        );
        payload.dimensions = null; // Send null if any dimension effectively becomes zero
      } else {
        logger.info(
          `[ShipStation API][Order ${payload.orderId}] Converting dimensions from inches to cm and scaling values for batch update.`
        );
        payload.dimensions = {
          units: 'cm',
          length: parseFloat(scaledLength.toFixed(DIMENSION_PRECISION)),
          width: parseFloat(scaledWidth.toFixed(DIMENSION_PRECISION)),
          height: parseFloat(scaledHeight.toFixed(DIMENSION_PRECISION)),
        };
        logger.info(
          `[ShipStation API][Order ${payload.orderId}] Sending cm: L ${payload.dimensions.length}, W ${payload.dimensions.width}, H ${payload.dimensions.height}`
        );
      }
    }
  } else {
    // If dimensionsInput was not provided, retain dimensions from freshlyFetchedOrder (already in payload via spread)
    // No conversion or nullification needed here as we respect the fetched state.
    logger.info(`[ShipStation API][Order ${payload.orderId}] No dimensionsInput provided, retaining dimensions from fresh fetch.`);
  }

  try {
    logger.info(
      `[ShipStation API] Attempting batch update for order ${freshlyFetchedOrder.orderNumber} (ID: ${freshlyFetchedOrder.orderId}). Payload includes updates for ${updatedOrderItems.length} item(s), internal notes, customer notes, and possibly dimensions.`
    );
    logger.debug(`[ShipStation API] Sending PAYLOAD to /orders/createorder: ${JSON.stringify({
      orderId: payload.orderId,
      items: payload.items.map((i: ShipStationOrderItem) => ({ // Typed item as ShipStationOrderItem
        lineItemKey: i.lineItemKey,
        options: i.options
      })),
      internalNotes: payload.internalNotes,
      customerNotes: payload.customerNotes,
      dimensions: payload.dimensions
    }, null, 2)}`);

    const response = await shipstationApi.post(endpoint, payload);

    logger.info(`[ShipStation API Response Details][Order ${payload.orderId}] Status: ${response.status} ${response.statusText}`);
    logger.info(`[ShipStation API Response Details][Order ${payload.orderId}] Data:`, JSON.stringify(response.data, null, 2));

    if (response.status === 200 || response.status === 201) {
      logger.info(`[ShipStation API] Batch update SUCCESS for order ${payload.orderId}.`);
      // Verify if the response data actually reflects the changes
      const responseOrder = response.data as ShipStationOrder;
      if (JSON.stringify(responseOrder.internalNotes) !== JSON.stringify(payload.internalNotes)) {
        logger.warn(`[ShipStation API] WARNING: internalNotes in response does not match sent payload for order ${payload.orderId}.`);
      }
      if (JSON.stringify(responseOrder.dimensions) !== JSON.stringify(payload.dimensions)) {
        logger.warn(`[ShipStation API] WARNING: dimensions in response does not match sent payload for order ${payload.orderId}.`);
      }
      return true;
    }

    logger.warn(
      `[ShipStation API] Batch update UNEXPECTED STATUS ${response.status} for order ${payload.orderId}. Full response details logged above.`
    );
    return false;

  } catch (error: unknown) {
    let msg = `[ShipStation API] FATAL ERROR during batch update POST for order ${payload.orderId}`;
    if (axios.isAxiosError(error)) {
      msg += `. Status: ${error.response?.status ?? 'N/A'}`;
      logger.error(`[ShipStation API Error Details][Order ${payload.orderId}] Full Axios Error:`, error);
      if (error.response) {
        msg += ` Response: ${JSON.stringify(error.response.data)}`;
        logger.error(`[ShipStation API Error Details][Order ${payload.orderId}] Response Status: ${error.response.status} ${error.response.statusText}`);
        logger.error(`[ShipStation API Error Details][Order ${payload.orderId}] Response Headers:`, JSON.stringify(error.response.headers, null, 2));
        logger.error(`[ShipStation API Error Details][Order ${payload.orderId}] Response Data:`, JSON.stringify(error.response.data, null, 2));
      } else if (error.request) {
        logger.error(`[ShipStation API Error Details][Order ${payload.orderId}] Request made but no response received:`, error.request);
      } else {
        logger.error(`[ShipStation API Error Details][Order ${payload.orderId}] Error setting up request: ${error.message}`);
      }
    } else if (error instanceof Error) {
      msg += `: ${error.message}`;
      logger.error(`[ShipStation API Error Details][Order ${payload.orderId}] Non-Axios Error:`, error);
    } else {
      logger.error(`[ShipStation API Error Details][Order ${payload.orderId}] Unknown error object:`, error);
    }
    logger.error(msg);
    return false;
  }
}

/**
 * Fetches tags from ShipStation. (From THEIRS)
 */
export const listTags = async () => {
  try {
    const response = await shipstationApi.get('/tags');
    return response.data;
  } catch (error: any) {
    logger.error('[ShipStation] Error fetching tags:', error.message);
    // Optionally, send a system notification for critical errors
    // await sendSystemNotification(
    //   'ShipStation Tag Fetch Failed',
    //   error.message,
    //   ErrorSeverity.ERROR,
    //   ErrorCategory.API_INTEGRATION,
    //   error
    // );
    throw error; // Re-throw or handle as per desired error strategy
  }
};

/**
 * Fetches specific order details (customerNotes, internalNotes) from ShipStation by ShipStation Order ID.
 * Uses provided API key and secret for this specific call, bypassing the global client's credentials.
 *
 * @param apiKey - The ShipStation API Key.
 * @param apiSecret - The ShipStation API Secret.
 * @param shipstationOrderId - The ShipStation Order ID (numeric).
 * @returns An object with customerNotes and internalNotes, or null if not found or error.
 */
export async function fetchShipStationOrderDetails(
  apiKey: string,
  apiSecret: string,
  shipstationOrderId: number | string // ShipStation Order ID can be string or number in different contexts
): Promise<{ customerNotes: string | null; internalNotes: string | null } | null> {
  const endpoint = `https://ssapi.shipstation.com/orders/${shipstationOrderId}`;
  const authHeader = `Basic ${Buffer.from(`${apiKey}:${apiSecret}`).toString('base64')}`;

  logger.info('[API] Attempting to fetch single order details from ShipStation for live notes.', { shipstationOrderId, endpoint });

  try {
    const response = await axios.get<ShipStationOrder>(endpoint, {
      headers: {
        Authorization: authHeader,
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 200 && response.data) {
      logger.info('[API] Successfully fetched order details from ShipStation.', { shipstationOrderId, customerNotesFetched: !!response.data.customerNotes, internalNotesFetched: !!response.data.internalNotes });
      return {
        customerNotes: response.data.customerNotes,
        internalNotes: response.data.internalNotes,
      };
    } else {
      logger.warn('[API] Received non-200 status or no data when fetching order details from ShipStation.', { shipstationOrderId, status: response.status });
      return null;
    }
  } catch (error: unknown) {
    let errorMessage = '[API] Error fetching single order details from ShipStation';
    let statusCode: number | string = 'N/A';

    if (axios.isAxiosError(error)) {
      statusCode = error.response?.status ?? 'N/A';
      errorMessage += `. Status: ${statusCode}.`;
      if (error.response?.data) {
        // Avoid logging potentially large data object in prod for security/verbosity
        logger.error(`${errorMessage} Response data preview: ${JSON.stringify(error.response.data).substring(0,200)}`, { shipstationOrderId, status: statusCode, responseDataPreview: JSON.stringify(error.response.data).substring(0, 200) });
      } else {
        logger.error(errorMessage, { shipstationOrderId, status: statusCode, error: error.message });
      }
    } else if (error instanceof Error) {
      errorMessage += `: ${error.message}`;
      logger.error(errorMessage, { shipstationOrderId, error: error.message });
    } else {
      logger.error(errorMessage + '. Unknown error type.', { shipstationOrderId, error });
    }
    return null;
  }
}

// Stub for backward compatibility (from THEIRS)
export const syncSingleOrder = async (orderId: string | number): Promise<{
  success: boolean;
  message: string;
  order?: any;
}> => {
  logger.info(`[Sync] Single order sync requested for order ID: ${orderId}`);
  try {
    const orderResponse = await fetchOrdersWithRetry({ orderId: Number(orderId) });

    if (orderResponse.orders && orderResponse.orders.length > 0) {
      const ssOrder = orderResponse.orders[0];
      logger.info(`[Sync] Successfully fetched order ${ssOrder.orderNumber} (${ssOrder.orderId}) from ShipStation.`);

      const result = await upsertOrderWithItems(ssOrder, { syncAllStatuses: true });

      if (result.success) {
        logger.info(`[Sync] Successfully upserted order ${ssOrder.orderNumber}.`);
        return {
          success: true,
          message: `Successfully synchronized order ${ssOrder.orderNumber}`,
          order: result.order
        };
      } else {
        logger.error(`[Sync] Failed to upsert order ${ssOrder.orderNumber}:`, result.errors);
        return {
          success: false,
          message: `Failed to upsert order ${ssOrder.orderNumber}: ${result.errors.map(e => e.error).join(', ')}`
        };
      }
    } else {
      logger.error(`[Sync] Order ID ${orderId} not found in ShipStation.`);
      return { success: false, message: `Order ID ${orderId} not found in ShipStation.` };
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`[Sync] Error syncing single order ${orderId}:`, error);
    return { success: false, message: `Error syncing order: ${errorMessage}` };
  }
};